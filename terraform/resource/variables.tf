variable "do_token" {
  type = string
  default = ""
}

variable "operation_endpoint" {
  type = string
  default = ""
}

variable "access_token" {
  type = string
  default = ""
}

data "http" "deployments" {
  url = var.operation_endpoint
  method = "GET"
  request_headers = {
    Accept = "application/json"
    Authorization = var.access_token
  }
}

variable "cloudflare_api_token" {
  type = string
  default = ""
}

variable "cloudflare_zone_id" {
  type = string
  default = ""
}
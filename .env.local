ENV=local
#ENV=production
#ENV=development

# Database Configuration
#DB_DRIVER=sqlite
#DB_HOST=localhost
#DB_PORT=5432
#DB_USER=user
#DB_PASSWORD=password
#DB_NAME=hexagonal_db

#tom
#DB_DRIVER=postgres
#DB_HOST=yamanote.proxy.rlwy.net
#DB_PORT=41166
#DB_USER=postgres
#DB_PASSWORD=dzoSCdymnNFWGAsoGlwxqSMumDBbMNTu
#DB_NAME=railway
#AUTO_MIGRATE=false
DB_DRIVER=postgres
DB_HOST=db-ops-admin-do-user-6617875-0.j.db.ondigitalocean.com
DB_PORT=25061
DB_USER=doadmin
DB_PASSWORD=AVNS_y9v1HfqjAw2kmKdH3UX
DB_NAME=ops-admin-pool
AUTO_MIGRATE=false

# Server Configuration
PORT=8080

# JWT Configuration
JWT_SECRET=secret

#DO_TOKEN=*********************************************************************** // tom
DO_TOKEN=***********************************************************************
DO_SELF_TOKEN=***********************************************************************
#TF_BACKEND_DB=postgres://postgres:<EMAIL>:41166/terraform-state?sslmode=require // tom
TF_BACKEND_DB=postgres://doadmin:<EMAIL>:25061/terraform-state?sslmode=require
TF_BACKEND_SCHEMA_CLUSTER=cluster
TF_BACKEND_SCHEMA_RESOURCE=resource
TF_BACKEND_SCHEMA_DNS=dns
TF_OPERATION_ENDPOINT=http://localhost:8080/api/v1

#CLOUDFLARE_API_TOKEN=****************************************
#CLOUDFLARE_ACCOUNT_ID=af4d86bbc2a57d8e3cd99a749e3adda8
#CLOUDFLARE_MASTER_ZONE_ID=ba24c276482312f9986da7b3b052fdc5
#CLOUDFLARE_MASTER_ZONE_NAME="bugkeeper.space"
CLOUDFLARE_API_TOKEN=****************************************
CLOUDFLARE_ACCOUNT_ID=706ee7270aa740854c70a3ab5651cb2b
CLOUDFLARE_MASTER_ZONE_ID="21ca7b229bebc1666e0fe021b84105f8"
CLOUDFLARE_MASTER_ZONE_NAME="irich.info"


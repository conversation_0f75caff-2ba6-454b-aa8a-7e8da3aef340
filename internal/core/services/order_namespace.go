package services

import (
	"errors"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type OrderNamespaceService struct {
	orderNamespaceRepo ports.OrderNamespaceRepository
	orderRepo          ports.OrderRepository
	namespaceRepo      ports.NamespaceRepository
	workspaceRepo      ports.WorkspaceRepository
	clusterRepo        ports.ClusterRepository
}

func NewOrderNamespaceService(
	orderNamespaceRepo ports.OrderNamespaceRepository,
	orderRepo ports.OrderRepository,
	namespaceRepo ports.NamespaceRepository,
	workspaceRepo ports.WorkspaceRepository,
	clusterRepo ports.ClusterRepository,
) ports.OrderNamespaceService {
	return &OrderNamespaceService{
		orderNamespaceRepo: orderNamespaceRepo,
		orderRepo:          orderRepo,
		namespaceRepo:      namespaceRepo,
		workspaceRepo:      workspaceRepo,
		clusterRepo:        clusterRepo,
	}
}

func (s *OrderNamespaceService) Create(orderID, namespaceID, userID uint64) (*domain.OrderNamespace, error) {
	if orderID == 0 {
		return nil, errors.New("order ID is required")
	}
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}
	if userID == 0 {
		return nil, errors.New("user ID is required")
	}

	// Verify order exists
	_, err := s.orderRepo.FindByID(orderID)
	if err != nil {
		return nil, errors.New("order not found")
	}

	// Verify namespace exists
	namespace, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	// Verify user has access to the namespace by checking if they have access to the cluster's workspace
	cluster, err := s.clusterRepo.FindByID(namespace.ClusterID, 0)
	if err != nil {
		return nil, errors.New("cluster not found")
	}

	// Check if user has access to the workspace
	workspaceFilter := &ports.WorkspaceFilter{
		UserID: &userID,
	}
	userWorkspaces, err := s.workspaceRepo.FindAll(workspaceFilter)
	if err != nil {
		return nil, errors.New("failed to check user workspace access")
	}

	hasAccess := false
	for _, workspace := range userWorkspaces {
		if workspace.ID == cluster.WorkspaceID {
			hasAccess = true
			break
		}
	}

	if !hasAccess {
		return nil, errors.New("user does not have access to this namespace")
	}

	// Create order namespace association
	orderNamespace := &domain.OrderNamespace{
		OrderID:     orderID,
		NamespaceID: namespaceID,
	}

	err = s.orderNamespaceRepo.Insert(orderNamespace)
	if err != nil {
		return nil, err
	}

	return orderNamespace, nil
}

func (s *OrderNamespaceService) GetAll(filter *ports.OrderNamespaceFilter) ([]*domain.OrderNamespace, error) {
	return s.orderNamespaceRepo.FindAll(filter)
}

func (s *OrderNamespaceService) GetAllByUser(userID uint64) ([]*domain.OrderNamespace, error) {
	if userID == 0 {
		return nil, errors.New("user ID is required")
	}

	filter := &ports.OrderNamespaceFilter{
		UserID: &userID,
	}

	return s.orderNamespaceRepo.FindAll(filter)
}

func (s *OrderNamespaceService) GetByID(id uint64) (*domain.OrderNamespace, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}

	orderNamespace, err := s.orderNamespaceRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("order namespace not found")
	}

	return orderNamespace, nil
}

func (s *OrderNamespaceService) Delete(id uint64) error {
	if id == 0 {
		return errors.New("id is required")
	}

	// Verify order namespace exists
	_, err := s.orderNamespaceRepo.FindByID(id)
	if err != nil {
		return errors.New("order namespace not found")
	}

	return s.orderNamespaceRepo.Delete(id)
}

package handlers

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type ProjectHandler struct {
	projectService ports.ProjectService
}

func NewProjectHandler(projectService ports.ProjectService) *ProjectHandler {
	return &ProjectHandler{
		projectService: projectService,
	}
}

func (h *ProjectHandler) CreateProject(c *fiber.Ctx) error {
	var req dto.CreateProjectRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Validate request
	if req.Name == "" {
		return response.Error(c, fiber.StatusBadRequest, "Project name is required")
	}
	if req.Slug == "" {
		return response.Error(c, fiber.StatusBadRequest, "Project slug is required")
	}
	if req.Type == "" {
		return response.Error(c, fiber.StatusBadRequest, "Project type is required")
	}
	if req.ClusterID == 0 {
		return response.Error(c, fiber.StatusBadRequest, "Cluster ID is required")
	}
	if len(req.Deployments) == 0 {
		return response.Error(c, fiber.StatusBadRequest, "At least one deployment is required")
	}

	// Validate deployments
	for i, deployment := range req.Deployments {
		if deployment.Name == "" {
			return response.Error(c, fiber.StatusBadRequest, "Deployment name is required")
		}
		if deployment.Image == "" {
			return response.Error(c, fiber.StatusBadRequest, "Deployment image is required")
		}
		if deployment.ContainerPort == 0 {
			return response.Error(c, fiber.StatusBadRequest, "Container port is required")
		}
		if len(deployment.Services) == 0 {
			return response.Error(c, fiber.StatusBadRequest, "At least one service is required per deployment")
		}

		// Validate services
		for j, service := range deployment.Services {
			if service.Name == "" {
				return response.Error(c, fiber.StatusBadRequest, "Service name is required")
			}
			if service.Port == "" {
				return response.Error(c, fiber.StatusBadRequest, "Service port is required")
			}
			if service.TargetPort == "" {
				return response.Error(c, fiber.StatusBadRequest, "Service target port is required")
			}
			if service.Type == "" {
				return response.Error(c, fiber.StatusBadRequest, "Service type is required")
			}
			//if service.IngressSpec.Host == "" {
			//	return response.Error(c, fiber.StatusBadRequest, "Ingress spec host is required")
			//}
			//if service.IngressSpec.Path == "" {
			//	return response.Error(c, fiber.StatusBadRequest, "Ingress spec path is required")
			//}
			//if service.IngressSpec.Port == 0 {
			//	return response.Error(c, fiber.StatusBadRequest, "Ingress spec port is required")
			//}

			// Validate service type
			validTypes := map[string]bool{
				"ClusterIP":    true,
				"NodePort":     true,
				"LoadBalancer": true,
			}
			if !validTypes[service.Type] {
				return response.Error(c, fiber.StatusBadRequest, "Invalid service type. Must be ClusterIP, NodePort, or LoadBalancer")
			}

			// Set default replicas if not specified
			if req.Deployments[i].Replicas == 0 {
				req.Deployments[i].Replicas = 1
			}

			_ = j // suppress unused variable warning
		}
	}

	project, err := h.projectService.CreateProject(&req)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Project created successfully", project)
}

func (h *ProjectHandler) CreateProjectWithTemplate(c *fiber.Ctx) error {
	var req dto.CreateProjectWithTemplateRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Validate request
	if req.TemplateID == 0 {
		return response.Error(c, fiber.StatusBadRequest, "Template ID is required")
	}
	if req.Name == "" {
		return response.Error(c, fiber.StatusBadRequest, "Project name is required")
	}

	project, err := h.projectService.CreateProjectWithTemplate(req.TemplateID, req.Name)
	if err != nil {
		// Handle specific error cases
		if err.Error() == "template not found" || err.Error() == "failed to get template project: record not found" {
			return response.Error(c, fiber.StatusNotFound, "Template project not found")
		}
		if err.Error() == "project name is required" || err.Error() == "template ID is required" {
			return response.Error(c, fiber.StatusBadRequest, err.Error())
		}
		if err.Error() == "failed to generate valid slug from name" {
			return response.Error(c, fiber.StatusBadRequest, "Invalid project name: unable to generate URL-friendly slug")
		}
		// Check if it's a template type validation error
		if len(err.Error()) > 30 && err.Error()[:30] == "namespace with ID" && err.Error()[len(err.Error())-17:] == "is not a template" {
			return response.Error(c, fiber.StatusBadRequest, "The specified namespace is not a template")
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Project created from template successfully", project)
}

func (h *ProjectHandler) UpdateProject(c *fiber.Ctx) error {
	// Get project ID from URL parameter
	id, err := c.ParamsInt("id")
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid project ID")
	}

	var req dto.UpdateProjectRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Validate request (same validation as CreateProject)
	if req.Name == "" {
		return response.Error(c, fiber.StatusBadRequest, "Project name is required")
	}
	if req.Slug == "" {
		return response.Error(c, fiber.StatusBadRequest, "Project slug is required")
	}
	if req.Type == "" {
		return response.Error(c, fiber.StatusBadRequest, "Project type is required")
	}
	if req.ClusterID == 0 {
		return response.Error(c, fiber.StatusBadRequest, "Cluster ID is required")
	}
	if len(req.Deployments) == 0 {
		return response.Error(c, fiber.StatusBadRequest, "At least one deployment is required")
	}

	// Validate deployments
	for i, deployment := range req.Deployments {
		if deployment.Name == "" {
			return response.Error(c, fiber.StatusBadRequest, "Deployment name is required")
		}
		if deployment.Image == "" {
			return response.Error(c, fiber.StatusBadRequest, "Deployment image is required")
		}
		if deployment.ContainerPort == 0 {
			return response.Error(c, fiber.StatusBadRequest, "Container port is required")
		}
		if len(deployment.Services) == 0 {
			return response.Error(c, fiber.StatusBadRequest, "At least one service is required per deployment")
		}

		// Validate services
		for j, service := range deployment.Services {
			if service.Name == "" {
				return response.Error(c, fiber.StatusBadRequest, "Service name is required")
			}
			if service.Port == "" {
				return response.Error(c, fiber.StatusBadRequest, "Service port is required")
			}
			if service.TargetPort == "" {
				return response.Error(c, fiber.StatusBadRequest, "Service target port is required")
			}
			if service.Type == "" {
				return response.Error(c, fiber.StatusBadRequest, "Service type is required")
			}
			if service.IngressSpec.Host == "" {
				return response.Error(c, fiber.StatusBadRequest, "Ingress spec host is required")
			}
			if service.IngressSpec.Path == "" {
				return response.Error(c, fiber.StatusBadRequest, "Ingress spec path is required")
			}
			if service.IngressSpec.Port == 0 {
				return response.Error(c, fiber.StatusBadRequest, "Ingress spec port is required")
			}

			// Validate service type
			validTypes := map[string]bool{
				"ClusterIP":    true,
				"NodePort":     true,
				"LoadBalancer": true,
			}
			if !validTypes[service.Type] {
				return response.Error(c, fiber.StatusBadRequest, "Invalid service type. Must be ClusterIP, NodePort, or LoadBalancer")
			}

			// Set default replicas if not specified
			if req.Deployments[i].Replicas == 0 {
				req.Deployments[i].Replicas = 1
			}

			_ = j // suppress unused variable warning
		}
	}

	project, err := h.projectService.UpdateProject(uint64(id), &req)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Project updated successfully", project)
}

func (h *ProjectHandler) GetAllProjects(c *fiber.Ctx) error {
	// Parse query parameters for filtering
	filter := &ports.ProjectFilter{}

	// Parse name filter
	if nameParam := c.Query("name"); nameParam != "" {
		filter.Name = &nameParam
	}

	// Parse slug filter
	if slugParam := c.Query("slug"); slugParam != "" {
		filter.Slug = &slugParam
	}

	// Parse type filter
	if typeParam := c.Query("type"); typeParam != "" {
		namespaceType := domain.NamespaceType(typeParam)
		filter.Type = &namespaceType
	}

	// Parse is_active filter
	if isActiveParam := c.Query("is_active"); isActiveParam != "" {
		if isActive, err := strconv.ParseBool(isActiveParam); err == nil {
			filter.IsActive = &isActive
		}
	}

	// Parse cluster_id filter
	if clusterIDParam := c.Query("cluster_id"); clusterIDParam != "" {
		if clusterID, err := strconv.ParseUint(clusterIDParam, 10, 64); err == nil {
			filter.ClusterID = &clusterID
		}
	}

	// Parse workspace_id filter
	if workspaceIDParam := c.Query("workspace_id"); workspaceIDParam != "" {
		if workspaceID, err := strconv.ParseUint(workspaceIDParam, 10, 64); err == nil {
			filter.WorkspaceID = &workspaceID
		}
	}

	projects, err := h.projectService.GetAllProjects(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Projects retrieved successfully", projects)
}

func (h *ProjectHandler) GetProject(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid project ID")
	}

	project, err := h.projectService.GetByID(id)
	if err != nil {
		return response.Error(c, fiber.StatusNotFound, "Project not found")
	}

	return response.Success(c, fiber.StatusOK, "Project retrieved successfully", project)
}

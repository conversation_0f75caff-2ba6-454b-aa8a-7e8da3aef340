package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

func SetupProjectRoutes(router fiber.Router, projectHandler *handlers.ProjectHandler) {
	// GET /projects - Get all projects
	router.Get("/projects", projectHandler.GetAllProjects)

	// GET /projects/:id - Get a single project by ID
	router.Get("/projects/:id", projectHandler.GetProject)

	// POST /projects - Create a comprehensive project (namespace + deployments + services + ingress)
	router.Post("/projects", projectHandler.CreateProject)

	// POST /projects/template - Create a project from a template
	router.Post("/projects/template", projectHandler.CreateProjectWithTemplate)

	// PUT /projects/:id - Update/upsert a comprehensive project by ID
	router.Put("/projects/:id", projectHandler.UpdateProject)
}
